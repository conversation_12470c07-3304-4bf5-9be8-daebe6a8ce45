#!/usr/bin/env python3
"""
Volume Safety Test
Tests that the system never opens oversized positions
"""

import sys
import os
import json

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from money_management.percent_risk import PercentRiskStrategy
from money_management.base_strategy import AccountInfo

def test_volume_safety():
    """Test volume safety limits"""
    print("VOLUME SAFETY TEST")
    print("=" * 50)
    
    # Load account settings
    with open('config/accounts.json', 'r') as f:
        accounts_data = json.load(f)
    
    account_config = accounts_data['accounts'][0]
    mm_settings = account_config['money_management_settings']
    
    print(f"Account Settings:")
    print(f"  Risk Percent: {mm_settings['risk_percent']}%")
    print(f"  Max Risk Multiplier: {mm_settings['max_risk_multiplier']}x")
    print(f"  Max Volume Per Trade: {mm_settings['max_volume_per_trade']} lots")
    print()
    
    # Create strategy with account settings
    strategy = PercentRiskStrategy(mm_settings)
    
    # Test with $70 account (similar to your real account)
    account_info = AccountInfo(
        balance=70.0,
        equity=70.0,
        margin=0.0,
        free_margin=70.0,
        margin_level=0.0,
        currency="USD",
        leverage=500
    )
    
    # Test scenarios that might cause large volumes
    test_scenarios = [
        {
            'symbol': 'EURUSD',
            'entry_price': 1.0850,
            'stop_loss': 1.0840,  # Very tight 10 pip stop
            'description': 'EURUSD 10 pip stop (tight)'
        },
        {
            'symbol': 'EURUSD',
            'entry_price': 1.0850,
            'stop_loss': 1.0845,  # Very tight 5 pip stop
            'description': 'EURUSD 5 pip stop (very tight)'
        },
        {
            'symbol': 'EURUSD',
            'entry_price': 1.0850,
            'stop_loss': 1.0830,  # Normal 20 pip stop
            'description': 'EURUSD 20 pip stop (normal)'
        },
        {
            'symbol': 'USDJPY',
            'entry_price': 149.85,
            'stop_loss': 149.75,  # 10 pip stop on JPY
            'description': 'USDJPY 10 pip stop'
        }
    ]
    
    market_data = {
        'pip_value': 10.0,  # $10 per pip for 1 lot EURUSD
        'pip_size': 0.0001,
        'min_volume': 0.01,
        'max_volume': 100.0
    }
    
    all_safe = True
    max_safe_volume = 0.05  # Maximum safe volume for small account
    
    for scenario in test_scenarios:
        print(f"Testing: {scenario['description']}")
        
        # Adjust market data for JPY pairs
        if 'JPY' in scenario['symbol']:
            market_data['pip_size'] = 0.01
            market_data['pip_value'] = 6.67
        else:
            market_data['pip_size'] = 0.0001
            market_data['pip_value'] = 10.0
        
        trade_params = strategy.calculate_position_size(
            account_info=account_info,
            symbol=scenario['symbol'],
            entry_price=scenario['entry_price'],
            stop_loss=scenario['stop_loss'],
            trade_history=[],
            market_data=market_data
        )
        
        pip_distance = abs(scenario['entry_price'] - scenario['stop_loss']) / market_data['pip_size']
        
        print(f"  Entry: {scenario['entry_price']:.5f}")
        print(f"  Stop Loss: {scenario['stop_loss']:.5f}")
        print(f"  Pip Distance: {pip_distance:.1f}")
        print(f"  Calculated Volume: {trade_params.volume:.3f} lots")
        print(f"  Risk Amount: ${trade_params.risk_amount:.2f}")
        
        # Safety checks
        volume_safe = trade_params.volume <= max_safe_volume
        risk_reasonable = trade_params.risk_amount <= account_info.balance * 0.1  # Max 10% risk
        
        print(f"  Volume Safe (<= {max_safe_volume}): {'YES' if volume_safe else 'NO'}")
        print(f"  Risk Reasonable (<= 10%): {'YES' if risk_reasonable else 'NO'}")
        
        if volume_safe and risk_reasonable:
            print(f"  Result: SAFE")
        else:
            print(f"  Result: DANGEROUS")
            all_safe = False
        
        print()
    
    # Overall result
    print("OVERALL RESULT:")
    if all_safe:
        print("ALL TESTS PASSED - Volume safety limits working correctly")
        print("System is safe for real account trading")
    else:
        print("SOME TESTS FAILED - Volume safety limits need fixing")
        print("DO NOT use on real account until fixed")
    
    return all_safe

def test_extreme_scenarios():
    """Test extreme scenarios that might break volume limits"""
    print("EXTREME SCENARIO TESTS")
    print("=" * 50)
    
    # Load account settings
    with open('config/accounts.json', 'r') as f:
        accounts_data = json.load(f)
    
    mm_settings = accounts_data['accounts'][0]['money_management_settings']
    strategy = PercentRiskStrategy(mm_settings)
    
    # Very small account
    small_account = AccountInfo(
        balance=50.0,
        equity=50.0,
        margin=0.0,
        free_margin=50.0,
        margin_level=0.0,
        currency="USD",
        leverage=500
    )
    
    # Extreme scenarios
    extreme_scenarios = [
        {
            'symbol': 'EURUSD',
            'entry_price': 1.0850,
            'stop_loss': 1.0849,  # 1 pip stop (extreme)
            'description': 'EURUSD 1 pip stop (extreme tight)'
        },
        {
            'symbol': 'EURUSD',
            'entry_price': 1.0850,
            'stop_loss': 1.0848,  # 2 pip stop
            'description': 'EURUSD 2 pip stop (very tight)'
        }
    ]
    
    market_data = {
        'pip_value': 10.0,
        'pip_size': 0.0001,
        'min_volume': 0.01,
        'max_volume': 100.0
    }
    
    max_safe_volume = 0.05
    all_safe = True
    
    for scenario in extreme_scenarios:
        print(f"Testing: {scenario['description']}")
        
        trade_params = strategy.calculate_position_size(
            account_info=small_account,
            symbol=scenario['symbol'],
            entry_price=scenario['entry_price'],
            stop_loss=scenario['stop_loss'],
            trade_history=[],
            market_data=market_data
        )
        
        pip_distance = abs(scenario['entry_price'] - scenario['stop_loss']) / market_data['pip_size']
        
        print(f"  Pip Distance: {pip_distance:.1f}")
        print(f"  Volume: {trade_params.volume:.3f} lots")
        print(f"  Risk: ${trade_params.risk_amount:.2f}")
        
        if trade_params.volume <= max_safe_volume:
            print(f"  Result: SAFE (volume capped)")
        else:
            print(f"  Result: DANGEROUS (volume too high)")
            all_safe = False
        
        print()
    
    return all_safe

def main():
    """Main test function"""
    print("VOLUME SAFETY VALIDATION")
    print("=" * 60)
    
    test1_passed = test_volume_safety()
    test2_passed = test_extreme_scenarios()
    
    print("FINAL ASSESSMENT:")
    print("=" * 60)
    
    if test1_passed and test2_passed:
        print("ALL VOLUME SAFETY TESTS PASSED")
        print("System is safe for real account trading")
        print("Maximum volume per trade is properly limited to 0.05 lots")
        return True
    else:
        print("VOLUME SAFETY TESTS FAILED")
        print("DO NOT use on real account until volume limits are fixed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

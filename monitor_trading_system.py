#!/usr/bin/env python3
"""
Trading System Monitor
Monitors the trading system for 30 minutes and validates all operations
"""

import sys
import os
import time
import json
import subprocess
from datetime import datetime, timedelta
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from mt5_integration.mt5_client import MT5Client
    from account_management.models import TradingAccount
    MT5_AVAILABLE = True
except ImportError:
    MT5_AVAILABLE = False

class TradingSystemMonitor:
    """Monitors trading system operations"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.monitoring_duration = 30  # 30 minutes
        self.log_files = {
            'trading_system': 'logs/trading_system.log',
            'trades': 'logs/trades.log',
            'ai_decisions': 'logs/ai_decisions.log',
            'errors': 'logs/errors.log'
        }
        self.alerts = []
        self.mt5_client = None
        
    def start_monitoring(self):
        """Start comprehensive monitoring"""
        print("TRADING SYSTEM MONITOR")
        print("=" * 60)
        print(f"Start Time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Duration: {self.monitoring_duration} minutes")
        print(f"End Time: {(self.start_time + timedelta(minutes=self.monitoring_duration)).strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # Initialize MT5 connection for monitoring
        self._initialize_mt5()
        
        # Get initial account state
        initial_state = self._get_account_state()
        print(f"Initial Account State:")
        print(f"  Balance: ${initial_state['balance']:.2f}")
        print(f"  Equity: ${initial_state['equity']:.2f}")
        print(f"  Open Positions: {initial_state['positions']}")
        print(f"  Pending Orders: {initial_state['orders']}")
        print()
        
        # Start the trading system
        print("Starting trading system...")
        trading_process = self._start_trading_system()
        
        if not trading_process:
            print("ERROR: Failed to start trading system")
            return False
        
        # Monitor for specified duration
        end_time = self.start_time + timedelta(minutes=self.monitoring_duration)
        check_interval = 30  # Check every 30 seconds
        
        try:
            while datetime.now() < end_time:
                current_time = datetime.now()
                elapsed = (current_time - self.start_time).total_seconds() / 60
                
                print(f"\n[{current_time.strftime('%H:%M:%S')}] Monitoring... ({elapsed:.1f}/{self.monitoring_duration} min)")
                
                # Check account state
                current_state = self._get_account_state()
                self._check_account_changes(initial_state, current_state)
                
                # Check logs for issues
                self._check_logs()
                
                # Check for dangerous trades
                self._check_trade_safety(current_state)
                
                # Display current status
                self._display_status(current_state)
                
                # Wait for next check
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            print("\nMonitoring interrupted by user")
        finally:
            # Stop trading system
            print("\nStopping trading system...")
            if trading_process:
                trading_process.terminate()
                trading_process.wait()
            
            # Final report
            self._generate_final_report(initial_state)
        
        return True
    
    def _initialize_mt5(self):
        """Initialize MT5 connection"""
        if not MT5_AVAILABLE:
            print("WARNING: MT5 not available for monitoring")
            return False
        
        try:
            self.mt5_client = MT5Client()
            if self.mt5_client.initialize():
                # Login to account
                with open('config/accounts.json', 'r') as f:
                    accounts_data = json.load(f)
                
                account_config = accounts_data['accounts'][0]
                account = TradingAccount(
                    account_id=account_config['account_id'],
                    account_number=account_config['account_number'],
                    server=account_config['server'],
                    username=str(account_config['account_number']),
                    password=account_config['password'],
                    strategy_type=account_config['strategy'],
                    money_management_type=account_config['money_management'],
                    symbols=[s['symbol'] for s in account_config['symbols']],
                    timeframes=[s['timeframe'] for s in account_config['symbols']]
                )
                
                if self.mt5_client.login(account):
                    print("MT5 connection established for monitoring")
                    return True
                else:
                    print("WARNING: MT5 login failed")
                    return False
            else:
                print("WARNING: MT5 initialization failed")
                return False
        except Exception as e:
            print(f"WARNING: MT5 setup error: {e}")
            return False
    
    def _get_account_state(self):
        """Get current account state"""
        state = {
            'balance': 0.0,
            'equity': 0.0,
            'positions': 0,
            'orders': 0,
            'position_details': [],
            'order_details': []
        }
        
        if self.mt5_client:
            try:
                account_info = self.mt5_client.get_account_info()
                if account_info:
                    state['balance'] = account_info.balance
                    state['equity'] = account_info.equity
                
                positions = self.mt5_client.get_positions()
                if positions:
                    state['positions'] = len(positions)
                    state['position_details'] = positions
                
                orders = self.mt5_client.get_orders()
                if orders:
                    state['orders'] = len(orders)
                    state['order_details'] = orders
                    
            except Exception as e:
                print(f"WARNING: Error getting account state: {e}")
        
        return state
    
    def _start_trading_system(self):
        """Start the trading system"""
        try:
            # Use the main trading system script
            process = subprocess.Popen(
                [sys.executable, 'trading_system_main.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Give it a moment to start
            time.sleep(5)
            
            # Check if it's still running
            if process.poll() is None:
                print("Trading system started successfully")
                return process
            else:
                stdout, stderr = process.communicate()
                print(f"Trading system failed to start:")
                print(f"STDOUT: {stdout}")
                print(f"STDERR: {stderr}")
                return None
                
        except Exception as e:
            print(f"Error starting trading system: {e}")
            return None
    
    def _check_account_changes(self, initial_state, current_state):
        """Check for account changes"""
        balance_change = current_state['balance'] - initial_state['balance']
        equity_change = current_state['equity'] - initial_state['equity']
        
        if abs(balance_change) > 0.01:
            alert = f"BALANCE CHANGE: ${balance_change:+.2f} (${initial_state['balance']:.2f} -> ${current_state['balance']:.2f})"
            self.alerts.append(alert)
            print(f"  ALERT: {alert}")
        
        if abs(equity_change) > 0.01:
            alert = f"EQUITY CHANGE: ${equity_change:+.2f} (${initial_state['equity']:.2f} -> ${current_state['equity']:.2f})"
            self.alerts.append(alert)
            print(f"  ALERT: {alert}")
    
    def _check_trade_safety(self, current_state):
        """Check for dangerous trades"""
        for position in current_state['position_details']:
            volume = position.get('volume', 0)
            symbol = position.get('symbol', 'UNKNOWN')
            profit = position.get('profit', 0)
            
            # Check for oversized positions
            if volume > 0.05:  # More than 0.05 lots
                alert = f"OVERSIZED POSITION: {symbol} {volume} lots (should be <= 0.05)"
                self.alerts.append(alert)
                print(f"  CRITICAL ALERT: {alert}")
            
            # Check for large losses
            if profit < -5.0:  # More than $5 loss
                alert = f"LARGE LOSS: {symbol} ${profit:.2f} loss"
                self.alerts.append(alert)
                print(f"  ALERT: {alert}")
    
    def _check_logs(self):
        """Check log files for errors"""
        for log_name, log_path in self.log_files.items():
            if os.path.exists(log_path):
                try:
                    # Check last few lines for errors
                    with open(log_path, 'r') as f:
                        lines = f.readlines()
                        recent_lines = lines[-10:] if len(lines) > 10 else lines
                        
                        for line in recent_lines:
                            if 'ERROR' in line.upper() or 'CRITICAL' in line.upper():
                                alert = f"LOG ERROR in {log_name}: {line.strip()}"
                                self.alerts.append(alert)
                                print(f"  LOG ALERT: {alert}")
                                
                except Exception as e:
                    print(f"  WARNING: Error reading {log_path}: {e}")
    
    def _display_status(self, current_state):
        """Display current status"""
        print(f"  Balance: ${current_state['balance']:.2f}")
        print(f"  Equity: ${current_state['equity']:.2f}")
        print(f"  Positions: {current_state['positions']}")
        print(f"  Orders: {current_state['orders']}")
        
        # Show position details
        for position in current_state['position_details']:
            symbol = position.get('symbol', 'UNKNOWN')
            volume = position.get('volume', 0)
            profit = position.get('profit', 0)
            print(f"    Position: {symbol} {volume} lots, P&L: ${profit:.2f}")
    
    def _generate_final_report(self, initial_state):
        """Generate final monitoring report"""
        final_state = self._get_account_state()
        
        print("\nFINAL MONITORING REPORT")
        print("=" * 60)
        
        # Account changes
        balance_change = final_state['balance'] - initial_state['balance']
        equity_change = final_state['equity'] - initial_state['equity']
        
        print(f"Account Changes:")
        print(f"  Balance: ${initial_state['balance']:.2f} -> ${final_state['balance']:.2f} ({balance_change:+.2f})")
        print(f"  Equity: ${initial_state['equity']:.2f} -> ${final_state['equity']:.2f} ({equity_change:+.2f})")
        print(f"  Positions: {initial_state['positions']} -> {final_state['positions']}")
        print(f"  Orders: {initial_state['orders']} -> {final_state['orders']}")
        
        # Alerts summary
        print(f"\nAlerts Generated: {len(self.alerts)}")
        if self.alerts:
            for alert in self.alerts[-10:]:  # Show last 10 alerts
                print(f"  - {alert}")
        else:
            print("  No alerts generated - system operating normally")
        
        # Safety assessment
        print(f"\nSafety Assessment:")
        
        # Check for dangerous volumes
        dangerous_volumes = [p for p in final_state['position_details'] if p.get('volume', 0) > 0.05]
        if dangerous_volumes:
            print(f"  CRITICAL: {len(dangerous_volumes)} oversized positions detected")
            for pos in dangerous_volumes:
                print(f"    {pos.get('symbol')} {pos.get('volume')} lots")
        else:
            print(f"  GOOD: All position sizes within safe limits")
        
        # Check for large losses
        large_losses = [p for p in final_state['position_details'] if p.get('profit', 0) < -5.0]
        if large_losses:
            print(f"  WARNING: {len(large_losses)} positions with large losses")
        else:
            print(f"  GOOD: No positions with excessive losses")
        
        # Overall assessment
        if balance_change < -3.0:
            print(f"  CRITICAL: Daily loss limit may be exceeded")
        elif len(dangerous_volumes) > 0:
            print(f"  CRITICAL: Oversized positions detected - system needs fixing")
        elif len(self.alerts) > 10:
            print(f"  WARNING: Many alerts generated - review system")
        else:
            print(f"  GOOD: System operating within safe parameters")

def main():
    """Main monitoring function"""
    monitor = TradingSystemMonitor()
    
    try:
        success = monitor.start_monitoring()
        return success
    except Exception as e:
        print(f"Monitoring failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

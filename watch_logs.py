#!/usr/bin/env python3
"""
Real-time log monitoring for trading system
"""

import time
import os
from datetime import datetime

def watch_logs():
    """Watch trading system logs in real-time"""
    log_files = {
        'Trading System': 'logs/trading_system.log',
        'AI Decisions': 'logs/ai_decisions.log',
        'Trades': 'logs/trades.log',
        'Errors': 'logs/errors.log'
    }
    
    # Get initial file positions
    file_positions = {}
    for name, path in log_files.items():
        if os.path.exists(path):
            with open(path, 'r') as f:
                f.seek(0, 2)  # Go to end of file
                file_positions[name] = f.tell()
        else:
            file_positions[name] = 0
    
    print("REAL-TIME LOG MONITORING")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Watching for new log entries...")
    print("=" * 60)
    
    try:
        while True:
            new_entries = False
            
            for name, path in log_files.items():
                if os.path.exists(path):
                    with open(path, 'r') as f:
                        f.seek(file_positions[name])
                        new_lines = f.readlines()
                        
                        if new_lines:
                            new_entries = True
                            print(f"\n[{datetime.now().strftime('%H:%M:%S')}] {name}:")
                            for line in new_lines:
                                line = line.strip()
                                if line:
                                    # Highlight important events
                                    if 'ERROR' in line or 'CRITICAL' in line:
                                        print(f"  ❌ {line}")
                                    elif 'WARNING' in line:
                                        print(f"  ⚠️  {line}")
                                    elif 'BUY' in line or 'SELL' in line:
                                        print(f"  📈 {line}")
                                    elif 'BALANCE_UPDATE' in line:
                                        print(f"  💰 {line}")
                                    elif 'AI_DECISION' in line:
                                        print(f"  🤖 {line}")
                                    else:
                                        print(f"  ℹ️  {line}")
                        
                        file_positions[name] = f.tell()
            
            if not new_entries:
                print(".", end="", flush=True)
            
            time.sleep(2)  # Check every 2 seconds
            
    except KeyboardInterrupt:
        print("\n\nMonitoring stopped by user")

if __name__ == "__main__":
    watch_logs()

#!/usr/bin/env python3
"""
Comprehensive test for money management fixes
Tests all critical issues identified and their fixes
"""

import sys
import json
import asyncio
from datetime import datetime, timedelta
sys.path.append('src')

from signal_generation.signal_generator import SignalGenerator
from account_management.account_manager import AccountManager
from money_management.base_strategy import AccountInfo
from money_management.percent_risk import PercentRiskStrategy

async def test_daily_loss_limit_enforcement():
    """Test if daily loss limit is properly enforced"""
    print("=" * 80)
    print("TEST 1: DAILY LOSS LIMIT ENFORCEMENT")
    print("=" * 80)
    
    # Create account manager and signal generator
    account_manager = AccountManager()
    account_manager.load_accounts()
    signal_gen = SignalGenerator(account_manager)
    
    # Mock account with loss exceeding daily limit
    account = {
        'account_id': 'demo1',
        'money_management_settings': {
            'max_daily_loss_percent': 5.0,  # 5% daily loss limit
            'max_daily_trades': 5,
            'max_open_positions': 2
        }
    }
    
    # Simulate daily PnL tracking with loss exceeding limit
    today = datetime.now().date()
    daily_key = f"demo1_{today}"
    signal_gen.daily_pnl[daily_key] = -10.0  # $10 loss on $100 account = 10% loss
    
    # Mock MT5 client to return account balance
    class MockMT5Client:
        def __init__(self):
            self.current_account = True
        
        def get_account_info(self):
            class MockBalance:
                balance = 100.0
            return MockBalance()
    
    signal_gen.mt5_client = MockMT5Client()
    
    # Test if daily loss limit blocks trading
    can_trade = signal_gen._check_risk_limits(account, is_multiple_tp=False)
    
    print(f"Account: demo1")
    print(f"Account Balance: $100.00")
    print(f"Daily Loss: $10.00 (10%)")
    print(f"Daily Loss Limit: 5%")
    print(f"Can Trade: {'YES' if can_trade else 'NO'}")
    print(f"Expected: NO")
    print(f"Result: {'✅ PASS' if not can_trade else '❌ FAIL - Daily loss limit not enforced!'}")
    return not can_trade

async def test_stop_loss_validation():
    """Test if stop loss validation works"""
    print("\n" + "=" * 80)
    print("TEST 2: MANDATORY STOP LOSS VALIDATION")
    print("=" * 80)
    
    # Test signal without stop loss
    signal_without_sl = {
        'action': 'BUY',
        'confidence': 0.8,
        'entry_price': 1.1000,
        'take_profit': 1.1050
        # Missing stop_loss
    }
    
    account = {
        'account_id': 'demo1',
        'money_management_settings': {
            '_risk_controls': {
                'require_stop_loss': True
            }
        }
    }
    
    # Create signal generator
    account_manager = AccountManager()
    signal_gen = SignalGenerator(account_manager)
    
    # Test validation
    risk_settings = signal_gen._get_account_risk_settings(account)
    should_reject = risk_settings.get('require_stop_loss', True) and not signal_without_sl.get('stop_loss')
    
    print(f"Signal: BUY EURUSD @ 1.1000, TP: 1.1050, SL: None")
    print(f"Stop Loss Required: {risk_settings.get('require_stop_loss', True)}")
    print(f"Stop Loss Provided: {signal_without_sl.get('stop_loss') is not None}")
    print(f"Should Reject: {'YES' if should_reject else 'NO'}")
    print(f"Expected: YES")
    print(f"Result: {'✅ PASS' if should_reject else '❌ FAIL - Stop loss validation not working!'}")
    return should_reject

async def test_multiple_tp_risk_calculation():
    """Test if multiple TP risk calculation is fixed"""
    print("\n" + "=" * 80)
    print("TEST 3: MULTIPLE TP RISK CALCULATION FIX")
    print("=" * 80)
    
    # Create money management strategy
    mm_settings = {
        'risk_percent': 2.0,  # 2% risk per trade
        'max_volume_per_trade': 0.1,
        'force_min_volume': True
    }
    
    strategy = PercentRiskStrategy(mm_settings)
    account_info = AccountInfo(1000.0, 1000.0, 0, 500.0, 1000, "USD", 500)
    
    market_data = {
        "pip_value": 10.0,
        "pip_size": 0.0001,
        "min_volume": 0.01,
        "max_volume": 100.0
    }
    
    # Calculate position size for 20 pip stop loss
    entry_price = 1.1000
    stop_loss = 1.0980  # 20 pips
    
    trade_params = strategy.calculate_position_size(
        account_info, "EURUSD", entry_price, stop_loss, [], market_data
    )
    
    # Simulate multiple TP levels
    tp_levels = [
        {'price': 1.1020, 'volume_percent': 50},  # 50% at 20 pips profit
        {'price': 1.1040, 'volume_percent': 30},  # 30% at 40 pips profit
        {'price': 1.1060, 'volume_percent': 20}   # 20% at 60 pips profit
    ]
    
    # OLD WAY (WRONG): Each TP level gets full position size
    old_total_volume = 0
    for tp in tp_levels:
        old_tp_volume = trade_params.volume * (tp['volume_percent'] / 100)
        old_total_volume += old_tp_volume
    
    # NEW WAY (CORRECT): Split total position size
    new_total_volume = trade_params.volume
    
    expected_risk = account_info.balance * (mm_settings['risk_percent'] / 100)
    old_risk_multiplier = old_total_volume / trade_params.volume
    new_risk_multiplier = new_total_volume / trade_params.volume
    
    print(f"Account Balance: ${account_info.balance:.2f}")
    print(f"Risk Percent: {mm_settings['risk_percent']}%")
    print(f"Expected Risk: ${expected_risk:.2f}")
    print(f"Calculated Volume: {trade_params.volume:.2f}")
    print(f"Calculated Risk: ${trade_params.risk_amount:.2f}")
    print()
    print(f"Multiple TP Levels: {len(tp_levels)}")
    for i, tp in enumerate(tp_levels, 1):
        print(f"  TP{i}: {tp['volume_percent']}% @ {tp['price']}")
    print()
    print(f"OLD METHOD (WRONG):")
    print(f"  Total Volume: {old_total_volume:.2f}")
    print(f"  Risk Multiplier: {old_risk_multiplier:.1f}x")
    print(f"  Total Risk: ${trade_params.risk_amount * old_risk_multiplier:.2f}")
    print()
    print(f"NEW METHOD (CORRECT):")
    print(f"  Total Volume: {new_total_volume:.2f}")
    print(f"  Risk Multiplier: {new_risk_multiplier:.1f}x")
    print(f"  Total Risk: ${trade_params.risk_amount:.2f}")
    print()
    
    is_fixed = abs(new_risk_multiplier - 1.0) < 0.01  # Should be exactly 1.0
    print(f"Risk Calculation Fixed: {'YES' if is_fixed else 'NO'}")
    print(f"Expected: YES")
    print(f"Result: {'✅ PASS' if is_fixed else '❌ FAIL - Multiple TP risk still amplified!'}")
    return is_fixed

async def test_account_settings_clarity():
    """Test if account settings are now clear and consistent"""
    print("\n" + "=" * 80)
    print("TEST 4: ACCOUNT SETTINGS CLARITY AND CONSISTENCY")
    print("=" * 80)
    
    # Load updated account config
    with open('config/accounts.json', 'r') as f:
        config = json.load(f)
    
    account = config['accounts'][0]
    mm_settings = account['money_management_settings']
    
    print("Updated Account Settings:")
    print(f"  Money Management Type: {account['money_management']}")
    print(f"  Comment: {mm_settings.get('_comment', 'No comment')}")
    print()
    print("Risk Settings:")
    for key, value in mm_settings.items():
        if not key.startswith('_'):
            print(f"  {key}: {value}")
    
    # Check for clarity improvements
    has_comment = '_comment' in mm_settings
    has_risk_controls = '_risk_controls' in mm_settings
    has_examples = '_money_management_examples' in config
    uses_percent_based = 'max_daily_loss_percent' in mm_settings
    
    print()
    print("Clarity Checks:")
    print(f"  Has explanatory comment: {'✅' if has_comment else '❌'}")
    print(f"  Has risk controls section: {'✅' if has_risk_controls else '❌'}")
    print(f"  Has MM examples in config: {'✅' if has_examples else '❌'}")
    print(f"  Uses percentage-based limits: {'✅' if uses_percent_based else '❌'}")
    
    all_checks_pass = has_comment and has_risk_controls and has_examples and uses_percent_based
    print()
    print(f"Settings Clarity Improved: {'YES' if all_checks_pass else 'NO'}")
    print(f"Expected: YES")
    print(f"Result: {'✅ PASS' if all_checks_pass else '❌ FAIL - Settings still unclear!'}")
    return all_checks_pass

async def main():
    """Run all tests"""
    print("COMPREHENSIVE MONEY MANAGEMENT FIXES TESTING")
    print("=" * 80)
    
    results = []
    
    # Run all tests
    results.append(await test_daily_loss_limit_enforcement())
    results.append(await test_stop_loss_validation())
    results.append(await test_multiple_tp_risk_calculation())
    results.append(await test_account_settings_clarity())
    
    # Summary
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED - Money management fixes are working!")
    else:
        print("⚠️ SOME TESTS FAILED - Additional fixes needed")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())
